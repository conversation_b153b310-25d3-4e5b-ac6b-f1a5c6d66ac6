---
type: "manual"
---

# 业务服务接入指导文档

本文档详细说明如何将新的业务服务接入到基于 Consul 的微服务架构中，实现服务发现、负载均衡和统一网关访问。

## 📋 目录

- [架构概述](#架构概述)
- [前置条件](#前置条件)
- [快速开始](#快速开始)
- [详细步骤](#详细步骤)
- [配置说明](#配置说明)
- [部署方式](#部署方式)
- [测试验证](#测试验证)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)

## 🏗️ 架构概述

### 分离部署架构

```
┌─────────────────────────────────────────────────────────────┐
│                        主服务（基础设施）                      │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────────────┐  │
│  │ Consul  │  │  Nginx  │  │  Redis  │  │   PostgreSQL    │  │
│  │ (服务发现)│  │ (网关)  │  │ (缓存)  │  │    (数据库)     │  │
│  └─────────┘  └─────────┘  └─────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ 网络连接
                              │
┌─────────────────────────────────────────────────────────────┐
│                        业务服务                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐  │
│  │   业务服务 A     │  │   业务服务 B     │  │  业务服务 C   │  │
│  │ (图片描述服务)   │  │  (用户服务)     │  │ (订单服务)   │  │
│  └─────────────────┘  └─────────────────┘  └──────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 服务访问流程

```
客户端请求 → Nginx网关:80 → Consul服务发现 → 业务服务:8000
```

## ✅ 前置条件

### 1. 主服务（基础设施）已部署

确保基础设施服务已经运行：

```bash
# 检查基础设施服务状态
docker-compose -f docker-compose.infrastructure.yml ps

# 应该看到以下服务正在运行：
# - consul-server
# - consul-template  
# - nginx-gateway
# - redis-cache
# - postgres-db
# - consul-ui-proxy
```

### 2. 网络连通性

确保业务服务可以访问基础设施服务：

```bash
# 测试 Consul 连接
curl http://localhost:8500/v1/status/leader

# 测试网关连接
curl http://localhost/gateway/status
```

### 3. 开发环境准备

- Docker 和 Docker Compose
- 业务服务的源代码
- 必要的环境变量配置

## 🚀 快速开始

### 1. 复制模板文件

```bash
# 复制业务服务 Docker Compose 模板
cp business-service-template.yml docker-compose.yml

# 复制环境变量模板
cp .env.business.example .env
```

### 2. 修改配置

编辑 `docker-compose.yml`：

```yaml
services:
  # 将 'your-business-service' 替换为你的服务名
  my-business-service:
    container_name: my-business-service-server
    hostname: my-business-service
    environment:
      - SERVICE_NAME=my-business-service
      - SERVICE_TAGS=api,business,my-service
      # ... 其他配置
```

编辑 `.env` 文件：

```bash
# 服务配置
SERVICE_NAME=my-business-service
SERVICE_PORT=8000
SERVICE_TAGS=api,business,my-service

# 业务特定配置
YOUR_API_KEY=your_actual_api_key
YOUR_CONFIG=your_actual_config
```

### 3. 启动业务服务

```bash
# 启动业务服务
docker-compose up -d

# 检查服务状态
docker-compose ps
```

### 4. 验证接入

```bash
# 检查服务是否注册到 Consul
curl http://localhost:8500/v1/catalog/service/my-business-service

# 通过网关访问服务
curl http://localhost/api/my-business-service/health
```

## 📝 详细步骤

### 步骤 1: 准备业务服务代码

确保你的业务服务包含以下必要组件：

#### 1.1 Consul 服务注册代码

创建或确保存在 `consul_service.py`：

```python
import aiohttp
import asyncio
import os
from typing import Optional

class ConsulService:
    def __init__(self):
        self.consul_host = os.getenv("CONSUL_HOST", "consul")
        self.consul_port = int(os.getenv("CONSUL_PORT", "8500"))
        self.service_name = os.getenv("SERVICE_NAME", "business-service")
        self.service_port = int(os.getenv("SERVICE_PORT", "8000"))
        self.service_tags = os.getenv("SERVICE_TAGS", "api").split(",")
        
    async def register_service(self):
        """注册服务到 Consul"""
        service_data = {
            "ID": f"{self.service_name}-{os.getenv('HOSTNAME', 'localhost')}",
            "Name": self.service_name,
            "Tags": self.service_tags,
            "Address": os.getenv("HOSTNAME", "localhost"),
            "Port": self.service_port,
            "Check": {
                "HTTP": f"http://{os.getenv('HOSTNAME', 'localhost')}:{self.service_port}/health",
                "Interval": "10s",
                "Timeout": "5s"
            }
        }
        
        async with aiohttp.ClientSession() as session:
            url = f"http://{self.consul_host}:{self.consul_port}/v1/agent/service/register"
            async with session.put(url, json=service_data) as response:
                if response.status == 200:
                    print(f"✓ 服务 {self.service_name} 注册成功")
                else:
                    print(f"✗ 服务注册失败: {response.status}")
```

#### 1.2 健康检查端点

确保你的 API 包含健康检查端点：

```python
from fastapi import FastAPI

app = FastAPI()

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": os.getenv("SERVICE_NAME")}

@app.get("/info")
async def service_info():
    return {
        "service": os.getenv("SERVICE_NAME"),
        "version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT"),
        "tags": os.getenv("SERVICE_TAGS", "").split(",")
    }
```

### 步骤 2: 配置 Docker 环境

#### 2.1 创建 Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制并安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY run_consul_api.py .

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --quiet --tries=1 --spider http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "run_consul_api.py"]
```

#### 2.2 配置 docker-compose.yml

基于模板文件修改：

```yaml
version: '3.9'

networks:
  microservices:
    name: microservices
    external: true

services:
  my-business-service:
    build: .
    container_name: my-business-service-server
    hostname: my-business-service
    environment:
      # Consul 配置
      - CONSUL_HOST=${CONSUL_HOST:-consul}
      - CONSUL_PORT=${CONSUL_PORT:-8500}
      
      # 服务配置
      - SERVICE_NAME=my-business-service
      - SERVICE_PORT=${SERVICE_PORT:-8000}
      - SERVICE_TAGS=${SERVICE_TAGS:-api,business}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      
      # 数据库配置
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      
      # 业务特定配置
      - YOUR_API_KEY=${YOUR_API_KEY}
    env_file:
      - .env
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8000/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    command: ["python", "run_consul_api.py"]
```

### 步骤 3: 环境变量配置

基于 `.env.business.example` 创建 `.env` 文件：

```bash
# 基础设施连接
CONSUL_HOST=consul
CONSUL_PORT=8500

# 如果基础设施在其他主机
# CONSUL_HOST=*************

# 服务配置
SERVICE_NAME=my-business-service
SERVICE_PORT=8000
SERVICE_TAGS=api,business,my-service
ENVIRONMENT=production

# 数据库连接
DATABASE_URL=****************************************/mydb
REDIS_URL=redis://redis:6379/0

# 业务特定配置
YOUR_API_KEY=your_actual_api_key
YOUR_CONFIG=your_actual_config
```

## 🚀 部署方式

### 方式一：同主机部署

如果业务服务与基础设施在同一主机：

```bash
# 1. 确保基础设施正在运行
docker-compose -f docker-compose.infrastructure.yml ps

# 2. 启动业务服务
docker-compose up -d

# 3. 验证服务注册
curl http://localhost:8500/v1/catalog/service/my-business-service
```

### 方式二：跨主机部署

如果业务服务在不同主机：

#### 在业务服务主机上：

1. 修改 `.env` 文件：

```bash
# 指向基础设施主机的 IP
CONSUL_HOST=*************
DATABASE_URL=**************************************************
REDIS_URL=redis://*************:6379/0
```

2. 创建外部网络：

```bash
# 创建与基础设施相同的网络
docker network create microservices
```

3. 启动服务：

```bash
docker-compose up -d
```

### 方式三：独立数据库部署

如果业务服务需要独立的数据库：

```yaml
# 在 docker-compose.yml 中取消注释数据库服务
services:
  my-business-service:
    # ... 服务配置
    depends_on:
      - my-business-postgres
    environment:
      - DATABASE_URL=**********************************************************************/business_db

  my-business-postgres:
    image: postgres:16-alpine
    container_name: my-business-postgres
    # ... 数据库配置
```

## ✅ 测试验证

### 1. 服务注册验证

```bash
# 检查服务是否注册到 Consul
curl http://localhost:8500/v1/catalog/service/my-business-service

# 检查服务健康状态
curl http://localhost:8500/v1/health/service/my-business-service
```

### 2. 网关路由验证

```bash
# 通过网关访问服务健康检查
curl http://localhost/api/my-business-service/health

# 通过网关访问服务信息
curl http://localhost/api/my-business-service/info

# 测试业务接口
curl http://localhost/api/my-business-service/your-endpoint
```

### 3. 负载均衡验证

```bash
# 扩展服务实例
docker-compose up -d --scale my-business-service=3

# 多次请求验证负载均衡
for i in {1..10}; do
  curl http://localhost/api/my-business-service/info
done
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 服务无法注册到 Consul

**症状**: 服务启动但在 Consul UI 中看不到

**解决方案**:
```bash
# 检查网络连接
docker exec my-business-service-server ping consul

# 检查 Consul 配置
docker exec my-business-service-server env | grep CONSUL

# 查看服务日志
docker-compose logs my-business-service
```

#### 2. 网关无法路由到服务

**症状**: 通过网关访问返回 502 或 404

**解决方案**:
```bash
# 检查 Nginx 配置
docker exec nginx-gateway cat /etc/nginx/conf.d/services.conf

# 检查 consul-template 日志
docker-compose -f docker-compose.infrastructure.yml logs consul-template

# 重新加载 Nginx 配置
docker exec nginx-gateway nginx -s reload
```

#### 3. 数据库连接失败

**症状**: 服务启动失败，数据库连接错误

**解决方案**:
```bash
# 检查数据库连接
docker exec my-business-service-server pg_isready -h postgres -p 5432

# 检查环境变量
docker exec my-business-service-server env | grep DATABASE

# 测试数据库连接
docker exec postgres-db psql -U user -d mydb -c "SELECT 1;"
```

## 💡 最佳实践

### 1. 服务命名规范

- 使用小写字母和连字符
- 避免使用下划线
- 保持名称简洁且有意义
- 例如: `user-service`, `order-service`, `payment-service`

### 2. 环境变量管理

- 使用 `.env` 文件管理配置
- 敏感信息使用 Docker secrets 或外部密钥管理
- 为不同环境准备不同的配置文件

### 3. 健康检查配置

- 实现有意义的健康检查逻辑
- 检查数据库连接、外部服务依赖
- 设置合适的超时和重试参数

### 4. 日志管理

- 使用结构化日志格式（JSON）
- 包含请求 ID 用于链路追踪
- 设置合适的日志级别

### 5. 监控和告警

- 暴露 Prometheus 指标端点
- 监控关键业务指标
- 设置服务可用性告警

### 6. 安全考虑

- 不暴露不必要的端口
- 使用网络隔离
- 实现适当的认证和授权
- 定期更新依赖和基础镜像

## 📚 参考资源

- [Consul 官方文档](https://www.consul.io/docs)
- [Docker Compose 网络配置](https://docs.docker.com/compose/networking/)
- [FastAPI 健康检查最佳实践](https://fastapi.tiangolo.com/)
- [微服务架构模式](https://microservices.io/)

## 🔄 服务生命周期管理

### 服务启动流程

1. **容器启动** → 2. **依赖检查** → 3. **服务注册** → 4. **健康检查** → 5. **接收流量**

### 服务停止流程

1. **停止接收新请求** → 2. **完成现有请求** → 3. **注销服务** → 4. **清理资源** → 5. **容器停止**

### 优雅关闭实现

```python
import signal
import asyncio

class GracefulShutdown:
    def __init__(self, consul_service):
        self.consul_service = consul_service
        self.should_exit = False

    def setup_signal_handlers(self):
        signal.signal(signal.SIGTERM, self.handle_signal)
        signal.signal(signal.SIGINT, self.handle_signal)

    def handle_signal(self, signum, frame):
        print(f"收到信号 {signum}，开始优雅关闭...")
        self.should_exit = True

    async def shutdown(self):
        # 注销服务
        await self.consul_service.deregister_service()
        # 等待现有请求完成
        await asyncio.sleep(5)
        print("服务已优雅关闭")
```

## 📊 监控和可观测性

### 指标收集

在你的服务中添加 Prometheus 指标：

```python
from prometheus_client import Counter, Histogram, generate_latest

# 定义指标
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')

@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time

    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    REQUEST_DURATION.observe(duration)

    return response

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

### 日志配置

```python
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "service": os.getenv("SERVICE_NAME"),
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        return json.dumps(log_entry)

# 配置日志
logging.basicConfig(level=logging.INFO)
handler = logging.StreamHandler()
handler.setFormatter(JSONFormatter())
logger = logging.getLogger(__name__)
logger.addHandler(handler)
```

## 🧪 测试策略

### 单元测试

```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.asyncio
async def test_service_registration():
    with patch('aiohttp.ClientSession.put') as mock_put:
        mock_put.return_value.__aenter__.return_value.status = 200

        consul_service = ConsulService()
        await consul_service.register_service()

        mock_put.assert_called_once()
```

### 集成测试

```python
@pytest.mark.asyncio
async def test_health_check_endpoint():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
```

### 端到端测试

```bash
#!/bin/bash
# e2e_test.sh

# 启动服务
docker-compose up -d

# 等待服务就绪
sleep 30

# 测试服务注册
curl -f http://localhost:8500/v1/catalog/service/my-business-service || exit 1

# 测试网关路由
curl -f http://localhost/api/my-business-service/health || exit 1

# 测试业务功能
curl -f http://localhost/api/my-business-service/your-endpoint || exit 1

echo "所有测试通过！"
```

## 🔐 安全配置

### API 认证

```python
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def verify_token(token: str = Depends(security)):
    if not verify_jwt_token(token.credentials):
        raise HTTPException(status_code=401, detail="Invalid token")
    return token

@app.get("/protected-endpoint")
async def protected_endpoint(token: str = Depends(verify_token)):
    return {"message": "Access granted"}
```

### 网络安全

```yaml
# docker-compose.yml 安全配置
services:
  my-business-service:
    # 限制容器权限
    user: "1000:1000"
    read_only: true

    # 安全选项
    security_opt:
      - no-new-privileges:true

    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
```

## 📈 性能优化

### 连接池配置

```python
import asyncpg
from aioredis import ConnectionPool

# PostgreSQL 连接池
async def create_db_pool():
    return await asyncpg.create_pool(
        os.getenv("DATABASE_URL"),
        min_size=5,
        max_size=20,
        command_timeout=60
    )

# Redis 连接池
redis_pool = ConnectionPool.from_url(
    os.getenv("REDIS_URL"),
    max_connections=20
)
```

### 缓存策略

```python
import aioredis
from functools import wraps

def cache_result(ttl: int = 3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"

            # 尝试从缓存获取
            cached = await redis.get(cache_key)
            if cached:
                return json.loads(cached)

            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await redis.setex(cache_key, ttl, json.dumps(result))

            return result
        return wrapper
    return decorator

@cache_result(ttl=1800)
async def expensive_operation(param: str):
    # 耗时操作
    return {"result": "computed_value"}
```

---

## 📞 支持和帮助

### 常用命令速查

```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f my-business-service

# 重启服务
docker-compose restart my-business-service

# 扩展服务实例
docker-compose up -d --scale my-business-service=3

# 查看 Consul 服务
curl http://localhost:8500/v1/catalog/services

# 查看服务健康状态
curl http://localhost:8500/v1/health/service/my-business-service

# 通过网关访问服务
curl http://localhost/api/my-business-service/health
```

### 问题诊断清单

- [ ] 基础设施服务是否正常运行？
- [ ] 网络连接是否正常？
- [ ] 环境变量配置是否正确？
- [ ] 服务是否成功注册到 Consul？
- [ ] 健康检查是否通过？
- [ ] Nginx 配置是否正确生成？
- [ ] 防火墙是否阻止了连接？

### 获取帮助

如有问题或需要帮助，请：

1. 查看项目的 GitHub Issues
2. 查阅相关文档和日志
3. 联系维护团队
4. 在社区论坛提问

---

**文档版本**: v1.0
**最后更新**: 2024-01-XX
**维护者**: 项目团队
