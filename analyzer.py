import time
import json
from itertools import product
from typing import List, Dict, Any, Optional

import requests
from get_product_detail import get_product_detail


# ============================= 占位函数 ============================
# 该函数用于将 dict 数据转换为 markdown 字符串，留空给用户自行实现
# -----------------------------------------------------------------

def change_md(data: Dict[str, Any]) -> str:  # noqa: D401
    """将 dict 数据转换为 markdown 字符串（占位实现）

    Args:
        data: 产品信息的字典形式。

    Returns:
        转换后的 markdown 字符串。
    """
    # TODO: 用户在此处填入实际的转换逻辑
    return ""  # 默认直接返回空字符串（表示不转换）


# ============================ 系统提示词 ===========================
# 按照用户要求原样嵌入，可根据需要动态格式化。
# -----------------------------------------------------------------
SYSTEM_PROMPT_TEMPLATE = """ 
 #任务
-将两个产品的介绍进行对比,按照我的评分规则得出综合相似评分,总分数范围1-100:
1,判断两个产品的1级类目是否相同,分数5-10分
2,判断两个产品的3级类目是否相似,分数15-25分
3,判断两个产品的标题相似性,分数15-25分
4,判断两个产品的价格相似性,分数10-15分
5,判断两个产的图片描述相似性,分数20-35分

##思考过程
###产品文案参数思考过程
-如果不在1级类目中,大概率产品相似性不相关,应该给予较低的相识评价
-2级类目是产品的细分类,考虑到一个产品可能有多个二级类目,所以你需要理解两个产品都适合彼此的2级类目
-标题中涉及到产品名字,参数,功能,风格,使用场景,产品名字是首要思考的对象,其他参数其次
-如果两个产品的产品名字一致,参数偏差10%视为相似,偏差太大,需要考虑价格偏差,价格偏差15%以内视为相似.反之先观察价格再观察参数也成立
-图片描述主要查看形状，功能,使用场景,风格等维度考虑.与文案描述结合综合判断

##不相似产品定义
-如果两个产品一级类目无相似关系,价格和材质不同，安装方式不同，功能描述不同，参数差异差50%以上的产品,视为不相关产品,判定为1

##扣减规则
-如果两个产品一级类目不同，判定为20分
-如果两个产品一级类目相似，三级类目相似,但是参数偏差20%以上或者安装方式不同视为不相似,材质差异大,价格偏差40%以上视为不相似,判定为20-40分
-如果两个产品一级类目相似,形状差异大,如圆形与长条型,圆盘型与圆柱型,圆形与不规则型,扣10-20分
-如果两个产品使用场景差异大,如大厅顶灯与桌面台灯,室外路灯与室内灯具,先查看3级类目是否类似,如果不类似,扣15-25分.否则不扣分



##产品信息:
###产品1:
    {target_product}


###产品2:
    {product}

##输出要求
只需要输出1-100的综合相似值,不需要输出任何的推理思考过程.1为完全不相似,100为完全相似

##Output format:
{{"similar_scores":相似值,"reson":中文分析说明}}

##示例
{{"similar_scores":40,"reson":中文分析说明}}
 """.strip()

# ========================== 核心比较类 ===========================
class EndpointConfig:
    """保存单个模型调用端点的配置信息。"""

    def __init__(self, url: str, api_key: str, model: str, is_multimodal: bool = False):
        self.url = url.rstrip("/")
        self.api_key = api_key
        self.model = model
        self.is_multimodal = is_multimodal

    def headers(self) -> Dict[str, str]:
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }


class RoundRobinSelector:
    """简单轮询选择器，在多个端点间切换。"""

    def __init__(self, endpoints: List['EndpointConfig']):
        if not endpoints:
            raise ValueError("endpoint 列表不能为空！")
        self._eps = endpoints
        self._idx = 0

    def next(self) -> 'EndpointConfig':
        ep = self._eps[self._idx]
        self._idx = (self._idx + 1) % len(self._eps)
        return ep

    @property
    def all(self) -> List['EndpointConfig']:
        return self._eps


class AIProductComparer:
    """产品相似度比较器，支持纯文本、多模态或二者并用。"""

    MM_COOLDOWN = 120  # 多模态 429/500 后冷却秒数
    RETRY_STATUS = {429, 500, 502, 503, 504}

    def __init__(
        self,
        text_endpoints: List[EndpointConfig],
        mm_endpoints: Optional[List[EndpointConfig]] = None,
        *,
        timeout: int = 120,          # 💡 默认超时时间：2 分钟
        temperature: float = 0.1,
    ) -> None:
        if not text_endpoints:
            raise ValueError("至少需要一个纯文本端点！")
        self.text_sel = RoundRobinSelector(text_endpoints)
        self.mm_sel = RoundRobinSelector(mm_endpoints) if mm_endpoints else None
        self.timeout = timeout
        self.temperature = temperature
        self._mm_disabled_until = 0

    # --------------------------- 主接口 ---------------------------

    def compare(
        self,
        product1: Dict[str, Any],
        product2: Dict[str, Any],
        *,
        mode: str = "both",  # text|multimodal|both
        convert: bool = False,
    ) -> Dict[str, Any]:
        p1_str = change_md(product1) if convert else json.dumps(product1, ensure_ascii=False)
        p2_str = change_md(product2) if convert else json.dumps(product2, ensure_ascii=False)
        prompt = SYSTEM_PROMPT_TEMPLATE.format(target_product=p1_str, product=p2_str)

        if mode == "multimodal":
            return self._call_with_retry(prompt, use_mm=True)
        elif mode == "text":
            return self._call_with_retry(prompt, use_mm=False)
        elif mode == "both":
            if self._mm_allowed():
                try:
                    return self._call_with_retry(prompt, use_mm=True)
                except Exception as exc:
                    if self._should_block_mm(exc):
                        self._mm_disabled_until = time.time() + self.MM_COOLDOWN
                        print("[WARN] 多模态不可用，降级文本。")
                    else:
                        raise
            return self._call_with_retry(prompt, use_mm=False)
        else:
            raise ValueError("mode 只能是 text/multimodal/both")

    def compare_by_ids(
        self,
        product_id1: int,
        product_id2: int,
        *,
        mode: str = "both",  # text|multimodal|both
        convert: bool = False,
    ) -> Dict[str, Any]:
        """
        通过两个产品ID直接获得匹配度

        Args:
            product_id1: 第一个产品的ID
            product_id2: 第二个产品的ID
            mode: 比较模式 (text|multimodal|both)
            convert: 是否转换为markdown格式

        Returns:
            包含相似度评分和分析说明的字典

        Raises:
            ValueError: 当无法获取产品信息时
        """
        # 获取两个产品的详细信息
        product1_info = get_product_detail(product_id1)
        product2_info = get_product_detail(product_id2)

        # 检查是否成功获取产品信息
        if product1_info is None:
            raise ValueError(f"无法获取产品ID {product_id1} 的信息")
        if product2_info is None:
            raise ValueError(f"无法获取产品ID {product_id2} 的信息")

        # 使用现有的compare方法进行比较
        return self.compare(product1_info, product2_info, mode=mode, convert=convert)

    # ------------------------- 工具方法 -------------------------

    def _mm_allowed(self) -> bool:
        return self.mm_sel is not None and time.time() >= self._mm_disabled_until

    @staticmethod
    def _should_block_mm(exc: Exception) -> bool:
        if isinstance(exc, requests.Timeout):
            return True
        if isinstance(exc, requests.HTTPError):
            return exc.response is not None and exc.response.status_code in AIProductComparer.RETRY_STATUS
        if isinstance(exc, RuntimeError) and ("429" in str(exc) or "timed out" in str(exc)):
            return True
        return False

    # --------------------- 核心调用 + 重试 ---------------------

    def _call_with_retry(self, prompt: str, *, use_mm: bool) -> Dict[str, Any]:
        """尝试所有备用端点，直到成功或全部失败。"""
        selector = self.mm_sel if use_mm else self.text_sel
        eps = selector.all
        last_err: Optional[Exception] = None
        for ep in eps:
            # 多模态模式下，跳过纯文本端点（理论上不会出现）
            if use_mm and not ep.is_multimodal:
                continue
            try:
                return self._call_once(prompt, ep)
            except (requests.Timeout, requests.HTTPError, RuntimeError) as err:
                # 仅当为可重试错误才继续；否则立刻抛出
                if isinstance(err, requests.HTTPError):
                    status = err.response.status_code if err.response is not None else None
                    if status not in self.RETRY_STATUS:
                        raise  # 不可重试错误
                # 记录最后错误，继续尝试下一个端点
                print(f"[WARN] 端点 {ep.url} 失败: {err}. 尝试下一个备用端点…")
                last_err = err
                continue
        # 全部失败
        assert last_err is not None  # for mypy
        raise last_err

    def _call_once(self, prompt: str, ep: EndpointConfig) -> Dict[str, Any]:
        payload = {
            "model": ep.model,
            "temperature": self.temperature,
            "messages": [{"role": "system", "content": prompt}],
        }
        try:
            resp = requests.post(
                f"{ep.url}/v1/chat/completions",
                headers=ep.headers(),
                json=payload,
                timeout=self.timeout,
            )
            resp.raise_for_status()
        except (requests.HTTPError, requests.Timeout) as err:
            raise err

        data = resp.json()
        try:
            content = data["choices"][0]["message"]["content"].strip()
            return json.loads(content)
        except Exception as e:
            raise RuntimeError(f"解析模型返回失败: {e}\n内容: {data}")


# ========================== 便捷函数 ===========================

def compare_products_by_ids(
    product_id1: int,
    product_id2: int,
    text_endpoints: List[EndpointConfig],
    mm_endpoints: Optional[List[EndpointConfig]] = None,
    *,
    mode: str = "text",
    convert: bool = False,
    timeout: int = 120,
    temperature: float = 0.1,
) -> Dict[str, Any]:
    """
    便捷函数：通过两个产品ID直接获得匹配度

    Args:
        product_id1: 第一个产品的ID
        product_id2: 第二个产品的ID
        text_endpoints: 纯文本模型端点列表
        mm_endpoints: 多模态模型端点列表（可选）
        mode: 比较模式 (text|multimodal|both)，默认为text
        convert: 是否转换为markdown格式，默认为False
        timeout: 请求超时时间（秒），默认120秒
        temperature: 模型温度参数，默认0.1

    Returns:
        包含相似度评分和分析说明的字典，格式如：
        {"similar_scores": 85, "reson": "两个产品在类目、标题等方面相似度较高"}

    Raises:
        ValueError: 当无法获取产品信息或参数错误时

    Example:
        >>> text_endpoints = [EndpointConfig("http://api.example.com", "api-key", "model-name")]
        >>> result = compare_products_by_ids(12345, 67890, text_endpoints)
        >>> print(f"相似度评分: {result['similar_scores']}")
        >>> print(f"分析说明: {result['reson']}")
    """
    # 创建比较器实例
    comparer = AIProductComparer(
        text_endpoints=text_endpoints,
        mm_endpoints=mm_endpoints,
        timeout=timeout,
        temperature=temperature,
    )

    # 执行比较
    return comparer.compare_by_ids(
        product_id1=product_id1,
        product_id2=product_id2,
        mode=mode,
        convert=convert,
    )


if __name__ == "__main__":
    # ---------- 1. 定义纯文本模型端点 ----------
    text_endpoints = [
        EndpointConfig(
            url="http://************:3000",  # 接口基础地址（不要带尾部斜杠）
            api_key="sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK",  # 该端点对应的 API‑Key
            model="deepseek/deepseek-chat-v3-0324:free",  # 要调用的模型名
            is_multimodal=False  # 纯文本 → False
        ),
        EndpointConfig(
            url="http://************:3000",  # 接口基础地址（不要带尾部斜杠）
            api_key="sk-xeqWxvtLPPLtxfxFrcWxmT9MjsUHB40KXyGBWwialVn99ogK",  # 该端点对应的 API‑Key
            model="deepseek-ai/DeepSeek-V3",  # 要调用的模型名
            is_multimodal=False  # 纯文本 → False
        ),
        # 还可以继续加更多纯文本端点 ...
    ]

    # ---------- 2. 定义多模态模型端点 ----------
    mm_endpoints = [
        EndpointConfig(
            url="https://mm-api.yourcompany.com",
            api_key="mm-key-abc",
            model="gpt-4o-mini-vision",  # 假设这是支持 vision 的版本
            is_multimodal=True  # 多模态 → True
        )
        # 也可以再加多个多模态端点，供轮询容错
    ]

    # ---------- 3. 创建比较器 ----------
    comparer = AIProductComparer(
        text_endpoints=text_endpoints,
        mm_endpoints=mm_endpoints,  # 没有多模态可传 None
        timeout=60  # 请求超时（秒）
    )

    # ---------- 4. 使用示例 ----------

    # 示例1: 使用便捷函数直接通过产品ID比较
    print("=== 示例1: 使用便捷函数比较两个产品 ===")
    try:
        # 假设有两个产品ID
        product_id1 = *********  # 替换为实际的产品ID
        product_id2 = *********  # 替换为实际的产品ID

        result = compare_products_by_ids(
            product_id1=product_id1,
            product_id2=product_id2,
            text_endpoints=text_endpoints,
            mode="text"
        )

        print(f"产品 {product_id1} 与产品 {product_id2} 的相似度评分: {result['similar_scores']}")
        print(f"分析说明: {result['reson']}")

    except Exception as e:
        print(f"比较失败: {e}")

    # 示例2: 使用类方法进行批量比较
    print("\n=== 示例2: 批量比较产品 ===")
    import random
    from concurrent.futures import ThreadPoolExecutor, as_completed

    # 示例产品ID列表（实际使用时替换为真实的产品ID）
    sample_product_ids = [*********, *********, 111222333, 444555666, 777888999]

    def run_id_comparison(id1, id2):
        """使用产品ID进行比较的任务函数"""
        try:
            result = comparer.compare_by_ids(id1, id2, mode="text")
            return {"id1": id1, "id2": id2, "result": result}
        except Exception as e:
            return {"id1": id1, "id2": id2, "error": str(e)}

    # 生成比较任务
    comparison_tasks = []
    for i in range(min(10, len(sample_product_ids))):  # 最多10个比较任务
        if len(sample_product_ids) >= 2:
            id1, id2 = random.sample(sample_product_ids, 2)
            comparison_tasks.append((id1, id2))

    # 执行批量比较
    if comparison_tasks:
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(run_id_comparison, id1, id2) for id1, id2 in comparison_tasks]

            for future in as_completed(futures):
                try:
                    result = future.result()
                    if "error" in result:
                        print(f"产品 {result['id1']} 与 {result['id2']} 比较失败: {result['error']}")
                    else:
                        comparison_result = result['result']
                        print(f"产品 {result['id1']} 与 {result['id2']} 相似度: {comparison_result['similar_scores']}")
                except Exception as e:
                    print(f"任务执行出错: {e}")

